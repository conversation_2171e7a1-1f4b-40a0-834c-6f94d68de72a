# 现场执法检查功能分析报告与开发计划

## 🔍 技术方案完整性和可行性评估报告

> **评估日期：** 2025-01-31
> **评估范围：** 「"环境监管一件事"表单」功能技术实现方案
> **评估结论：** 方案基本可行，需要补充关键技术细节

## 📋 项目概述

本文档分析了现场执法检查系统中「"环境监管一件事"表单」功能的实现现状，并提供了完整的技术实现方案和开发计划。

**需求描述：**
- 在现有检查项功能基础上新增「"环境监管一件事"表单」功能
- 两个功能互斥显示：选择「"环境监管一件事"表单」时隐藏原检查项；选择原检查项时隐藏「"环境监管一件事"表单」
- 不能影响原有检查项的任何功能

## 🔍 现状分析报告

### 1. 当前系统架构分析

#### 前端实现状况
- ✅ **UI界面完整**：环境监管一件事表单UI已在`xczf-xckfb.jsp`中完整实现
- ✅ **树形结构**：使用Bootstrap折叠面板实现两级树形结构展示
- ✅ **交互逻辑**：二级检查项的单选逻辑（是/否/不涉及）已实现
- ✅ **问题简述**：模态框功能已实现，支持问题详细描述
- ✅ **级联控制**：一级面板"不涉及"复选框及双向级联逻辑已实现
- ✅ **互斥显示**：两种表单的切换显示逻辑已实现

#### 后端实现状况
- ✅ **数据服务**：`CheckItemConfigService`已实现树形数据获取
- ✅ **数据库表**：`CHECK_ITEM_CONFIG`表已创建并配置完整
- ✅ **数据传递**：树形结构通过`checkItemTree`正确传递到前端
- ✅ **业务逻辑**：树形结构构建和排序逻辑完整

#### 数据库结构现状
```sql
-- 现场检查主表
LOCAL_CHECK (ID, TASK_ID, OBJECT_NAME, ADDRESS, ...)

-- 检查项明细表
LOCAL_CHECK_ITEM (ID, CHECK_ITEM_NAME, CHECK_ITEM_RESULT, LOCAL_CHECK_ID, ...)

-- 环境监管一件事配置表
CHECK_ITEM_CONFIG (ID, ITEM_NAME, PARENT_ID, ITEM_SORT, CREATE_TIME, REMARK)
```

### 2. 缺失功能识别

#### 主要缺失功能
- ❌ **数据保存逻辑**：环境监管一件事表单的数据保存功能未实现
- ❌ **表单类型区分**：系统无法区分当前使用的是哪种表单类型
- ❌ **历史数据加载**：无法正确加载和显示环境监管一件事的历史数据
- ❌ **数据持久化**：问题简述等数据无法持久化存储

#### 技术债务
- 前端数据收集逻辑不完整
- 后端保存接口需要扩展
- 数据库表结构需要扩展以支持新表单类型

## 🏗️ 技术实现方案

### 1. 数据库扩展方案

#### 扩展 LOCAL_CHECK 表
```sql
-- 添加表单类型字段
ALTER TABLE LOCAL_CHECK ADD FORM_TYPE VARCHAR2(20) DEFAULT '0';
COMMENT ON COLUMN LOCAL_CHECK.FORM_TYPE IS '表单类型：0=原有检查项，1=环境监管一件事';
```

#### 扩展 LOCAL_CHECK_ITEM 表
```sql
-- 添加支持环境监管一件事的字段
ALTER TABLE LOCAL_CHECK_ITEM ADD FORM_TYPE VARCHAR2(20) DEFAULT '0';
ALTER TABLE LOCAL_CHECK_ITEM ADD CONFIG_ITEM_ID VARCHAR2(100);
ALTER TABLE LOCAL_CHECK_ITEM ADD PROBLEM_DESC CLOB;

COMMENT ON COLUMN LOCAL_CHECK_ITEM.FORM_TYPE IS '表单类型：0=原有检查项，1=环境监管一件事';
COMMENT ON COLUMN LOCAL_CHECK_ITEM.CONFIG_ITEM_ID IS '关联CHECK_ITEM_CONFIG表ID';
COMMENT ON COLUMN LOCAL_CHECK_ITEM.PROBLEM_DESC IS '问题简述';
```

### 2. 后端扩展方案

#### 实体类扩展
```java
// LocalCheck.java 添加字段
private String formType; // 表单类型：0=原有检查项，1=环境监管一件事

// LocalCheckItem.java 添加字段
private String formType; // 表单类型
private String configItemId; // 关联CHECK_ITEM_CONFIG表ID
private String problemDesc; // 问题简述
```

#### Service层扩展
```java
// LocalExamineService.java 扩展方法
ResponseJson saveLocalExamine(LocalCheck localCheck, String taskId,
    String chickItemList, SysUsers sysUser, List<SysFiles> filesList,
    String formType, String envSupervisionData) throws Exception;

// 新增环境监管一件事数据处理方法
void saveEnvSupervisionItems(String localCheckId, String envSupervisionData) throws Exception;
List<LocalCheckItem> loadEnvSupervisionItems(String localCheckId) throws Exception;
```

#### Controller层扩展
```java
// LocalExamineController.java 扩展保存方法
@RequestMapping(value = "/saveLocalExamine", method = RequestMethod.POST)
public ResponseJson saveLocalExamine(
    LocalCheck localCheck, String chickItemList, String taskId,
    String formType, String envSupervisionData) throws Exception {

    // 根据formType选择不同的保存逻辑
    if ("1".equals(formType)) {
        // 环境监管一件事保存逻辑
        return LocalExamineService.saveLocalExamine(localCheck, taskId,
            null, sysUser, filesList, formType, envSupervisionData);
    } else {
        // 原有检查项保存逻辑（保持不变）
        return LocalExamineService.saveLocalExamine(localCheck, taskId,
            chickItemList, sysUser, filesList, "0", null);
    }
}
```

### 3. 前端扩展方案

#### JavaScript保存逻辑扩展
```javascript
// 扩展保存按钮点击事件
$("#submitLocalExamineForm").click(function() {
    // 检测当前显示的表单类型
    var isEnvSupervision = $('#envSupervisionForm').is(':visible');

    if (isEnvSupervision) {
        // 收集环境监管一件事表单数据
        var envData = collectEnvSupervisionData();
        $("#envSupervisionData").val(JSON.stringify(envData));
        $("#formType").val("1");

        // 验证环境监管一件事表单数据
        if (!validateEnvSupervisionData(envData)) {
            return false;
        }
    } else {
        // 原有逻辑保持不变
        $("#formType").val("0");
        // 继续原有的vue.items数据收集逻辑
    }

    // 继续原有保存逻辑...
});
```
